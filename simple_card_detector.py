import cv2
import numpy as np
import os
from datetime import datetime

def detect_and_capture_cards(camera_index=1):
    """
    Phiên bản đơn giản để detect và chụp business card
    Args:
        camera_index (int): Index của camera (0: webcam tích hợp, 1,2,3...: camera USB)
    """
    # Tạo thư mục Image nếu chưa tồn tại
    os.makedirs("Image", exist_ok=True)

    # Thử kết nối với camera USB
    print(f"Đang thử kết nối camera index {camera_index}...")
    cap = cv2.VideoCapture(camera_index)
    
    if not cap.isOpened():
        print(f"Lỗi: Không thể mở camera index {camera_index}!")
        print("Thử các camera index khác:")
        for i in range(5):
            test_cap = cv2.VideoCapture(i)
            if test_cap.isOpened():
                print(f"  - Camera index {i}: Có sẵn")
                test_cap.release()
            else:
                print(f"  - Camera index {i}: Không có")
        return

    # Thiết lập độ phân giải cho camera USB
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

    print(f"✓ Đã kết nối thành công camera index {camera_index}")
    print("Nhấn SPACE để chụp ảnh, 'q' để thoát")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Chuyển sang grayscale để xử lý
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Blur để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Edge detection
        edges = cv2.Canny(blurred, 50, 150)
        
        # Tìm contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Lọc contours có thể là business card
        card_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Lọc theo diện tích (business card thường có diện tích từ 5000-50000 pixels)
            if 5000 < area < 50000:
                # Tính bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                # Business card thường có tỷ lệ từ 1.4 đến 2.0
                if 1.4 < aspect_ratio < 2.0:
                    card_contours.append((contour, area, (x, y, w, h)))
        
        # Sắp xếp theo diện tích giảm dần
        card_contours.sort(key=lambda x: x[1], reverse=True)
        
        # Vẽ detection lên frame
        display_frame = frame.copy()
        for i, (contour, area, bbox) in enumerate(card_contours[:3]):  # Chỉ hiển thị 3 card tốt nhất
            x, y, w, h = bbox
            
            # Vẽ bounding box
            color = (0, 255, 0) if i == 0 else (0, 255, 255)
            cv2.rectangle(display_frame, (x, y), (x + w, y + h), color, 2)
            
            # Hiển thị thông tin
            cv2.putText(display_frame, f"Card {i+1}", (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # Hiển thị số lượng card detect được
        cv2.putText(display_frame, f"Cards: {len(card_contours)}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Hiển thị hướng dẫn
        cv2.putText(display_frame, "SPACE: Capture, Q: Quit", (10, display_frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Hiển thị frame
        cv2.imshow('Business Card Detector', display_frame)
        
        # Xử lý phím nhấn
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q'):
            break
        elif key == ord(' ') and len(card_contours) > 0:
            # Chụp ảnh card tốt nhất
            contour, area, bbox = card_contours[0]
            x, y, w, h = bbox
            
            # Cắt ảnh card
            card_image = frame[y:y+h, x:x+w]
            
            # Tạo tên file với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"card_{timestamp}.jpg"
            filepath = os.path.join("Image", filename)
            
            # Lưu ảnh
            cv2.imwrite(filepath, card_image)
            print(f"✓ Đã lưu card: {filepath}")
            
            # Hiển thị thông báo trên màn hình
            cv2.putText(display_frame, "SAVED!", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
            cv2.imshow('Business Card Detector', display_frame)
            cv2.waitKey(500)  # Hiển thị thông báo trong 0.5 giây
    
    # Giải phóng tài nguyên
    cap.release()
    cv2.destroyAllWindows()
    print("Đã đóng camera")

def find_available_cameras():
    """
    Tìm tất cả camera có sẵn trong hệ thống
    """
    print("Đang quét camera có sẵn...")
    available_cameras = []

    for i in range(10):  # Kiểm tra từ index 0 đến 9
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                available_cameras.append(i)
                print(f"✓ Camera index {i}: Hoạt động tốt")
            else:
                print(f"⚠ Camera index {i}: Có thể kết nối nhưng không đọc được frame")
            cap.release()

    return available_cameras

if __name__ == "__main__":
    # Tìm camera có sẵn
    cameras = find_available_cameras()

    if not cameras:
        print("Không tìm thấy camera nào!")
    else:
        print(f"\nCác camera có sẵn: {cameras}")

        # Mặc định sử dụng camera USB (thường là index 1)
        # Nếu chỉ có 1 camera thì dùng index 0
        if len(cameras) == 1:
            camera_to_use = cameras[0]
        else:
            # Ưu tiên camera USB (index > 0)
            usb_cameras = [c for c in cameras if c > 0]
            camera_to_use = usb_cameras[0] if usb_cameras else cameras[0]

        print(f"Sử dụng camera index: {camera_to_use}")
        detect_and_capture_cards(camera_to_use)
