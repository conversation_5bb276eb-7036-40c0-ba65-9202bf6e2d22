import cv2
import numpy as np
import os
from datetime import datetime

def detect_and_capture_cards():
    """
    Phiên bản đơn giản để detect và chụp business card
    """
    # Tạo thư mục Image nếu chưa tồn tại
    os.makedirs("Image", exist_ok=True)
    
    # Khởi tạo camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("Lỗi: Không thể mở camera!")
        return
    
    print("Nhấn SPACE để chụp ảnh, 'q' để thoát")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # <PERSON><PERSON><PERSON><PERSON> sang grayscale để xử lý
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Blur để giảm noise
        blurred = cv2.G<PERSON>sianBlur(gray, (5, 5), 0)
        
        # Edge detection
        edges = cv2.Canny(blurred, 50, 150)
        
        # Tìm contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Lọc contours có thể là business card
        card_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # Lọc theo diện tích (business card thường có diện tích từ 5000-50000 pixels)
            if 5000 < area < 50000:
                # Tính bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                # Business card thường có tỷ lệ từ 1.4 đến 2.0
                if 1.4 < aspect_ratio < 2.0:
                    card_contours.append((contour, area, (x, y, w, h)))
        
        # Sắp xếp theo diện tích giảm dần
        card_contours.sort(key=lambda x: x[1], reverse=True)
        
        # Vẽ detection lên frame
        display_frame = frame.copy()
        for i, (contour, area, bbox) in enumerate(card_contours[:3]):  # Chỉ hiển thị 3 card tốt nhất
            x, y, w, h = bbox
            
            # Vẽ bounding box
            color = (0, 255, 0) if i == 0 else (0, 255, 255)
            cv2.rectangle(display_frame, (x, y), (x + w, y + h), color, 2)
            
            # Hiển thị thông tin
            cv2.putText(display_frame, f"Card {i+1}", (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # Hiển thị số lượng card detect được
        cv2.putText(display_frame, f"Cards: {len(card_contours)}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Hiển thị hướng dẫn
        cv2.putText(display_frame, "SPACE: Capture, Q: Quit", (10, display_frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Hiển thị frame
        cv2.imshow('Business Card Detector', display_frame)
        
        # Xử lý phím nhấn
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q'):
            break
        elif key == ord(' ') and len(card_contours) > 0:
            # Chụp ảnh card tốt nhất
            contour, area, bbox = card_contours[0]
            x, y, w, h = bbox
            
            # Cắt ảnh card
            card_image = frame[y:y+h, x:x+w]
            
            # Tạo tên file với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"card_{timestamp}.jpg"
            filepath = os.path.join("Image", filename)
            
            # Lưu ảnh
            cv2.imwrite(filepath, card_image)
            print(f"✓ Đã lưu card: {filepath}")
            
            # Hiển thị thông báo trên màn hình
            cv2.putText(display_frame, "SAVED!", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
            cv2.imshow('Business Card Detector', display_frame)
            cv2.waitKey(500)  # Hiển thị thông báo trong 0.5 giây
    
    # Giải phóng tài nguyên
    cap.release()
    cv2.destroyAllWindows()
    print("Đã đóng camera")

if __name__ == "__main__":
    detect_and_capture_cards()
