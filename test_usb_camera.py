import cv2

def test_cameras():
    """
    Test tất cả camera có sẵn và hiển thị video từ camera USB
    """
    print("=== KIỂM TRA CAMERA CÓ SẴN ===")
    
    available_cameras = []
    
    # Kiểm tra từ index 0 đến 5
    for i in range(6):
        print(f"Đang kiểm tra camera index {i}...")
        cap = cv2.VideoCapture(i)
        
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                available_cameras.append({
                    'index': i,
                    'resolution': f"{width}x{height}",
                    'working': True
                })
                print(f"  ✓ Camera {i}: Hoạt động - Độ phân giải: {width}x{height}")
            else:
                available_cameras.append({
                    'index': i,
                    'resolution': "Unknown",
                    'working': False
                })
                print(f"  ⚠ Camera {i}: Kết nối được nhưng không đọc được frame")
            cap.release()
        else:
            print(f"  ✗ Camera {i}: Không có")
    
    working_cameras = [cam for cam in available_cameras if cam['working']]
    
    if not working_cameras:
        print("\n❌ Không tìm thấy camera nào hoạt động!")
        return
    
    print(f"\n✅ Tìm thấy {len(working_cameras)} camera hoạt động:")
    for cam in working_cameras:
        cam_type = "Webcam tích hợp" if cam['index'] == 0 else "Camera USB"
        print(f"  - Index {cam['index']}: {cam_type} ({cam['resolution']})")
    
    # Chọn camera để test
    if len(working_cameras) == 1:
        selected_camera = working_cameras[0]['index']
        print(f"\nSử dụng camera duy nhất: Index {selected_camera}")
    else:
        # Ưu tiên camera USB (index > 0)
        usb_cameras = [cam for cam in working_cameras if cam['index'] > 0]
        if usb_cameras:
            selected_camera = usb_cameras[0]['index']
            print(f"\nSử dụng camera USB: Index {selected_camera}")
        else:
            selected_camera = working_cameras[0]['index']
            print(f"\nSử dụng camera: Index {selected_camera}")
    
    # Test camera được chọn
    print(f"\n=== TEST CAMERA INDEX {selected_camera} ===")
    print("Nhấn 'q' để thoát, 's' để chụp ảnh test")
    
    cap = cv2.VideoCapture(selected_camera)
    
    if not cap.isOpened():
        print("❌ Lỗi: Không thể mở camera!")
        return
    
    # Thiết lập độ phân giải
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    
    # Lấy độ phân giải thực tế
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"Độ phân giải thực tế: {actual_width}x{actual_height}")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("❌ Không thể đọc frame từ camera!")
            break
        
        frame_count += 1
        
        # Hiển thị thông tin trên frame
        cv2.putText(frame, f"Camera Index: {selected_camera}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, f"Resolution: {actual_width}x{actual_height}", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, f"Frame: {frame_count}", (10, 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, "Press 'q' to quit, 's' to save test image", (10, frame.shape[0] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Hiển thị frame
        cv2.imshow(f'USB Camera Test - Index {selected_camera}', frame)
        
        # Xử lý phím nhấn
        key = cv2.waitKey(1) & 0xFF
        
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Lưu ảnh test
            import os
            from datetime import datetime
            
            os.makedirs("Image", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_camera_{selected_camera}_{timestamp}.jpg"
            filepath = os.path.join("Image", filename)
            
            cv2.imwrite(filepath, frame)
            print(f"✓ Đã lưu ảnh test: {filepath}")
    
    # Giải phóng tài nguyên
    cap.release()
    cv2.destroyAllWindows()
    print("✅ Đã đóng camera test")

if __name__ == "__main__":
    test_cameras()
