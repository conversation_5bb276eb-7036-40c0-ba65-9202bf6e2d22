import cv2
import numpy as np
import os
from datetime import datetime
import time

class BusinessCardDetector:
    def __init__(self, save_folder="Image"):
        """
        Khởi tạo detector cho business card
        
        Args:
            save_folder (str): Th<PERSON> mục lưu ảnh card đư<PERSON><PERSON> detect
        """
        self.save_folder = save_folder
        self.last_capture_time = 0
        self.capture_cooldown = 2  # Thời gian chờ giữa các lần chụp (giây)
        
        # T<PERSON><PERSON> thư mục lưu ảnh nếu chưa tồn tại
        os.makedirs(save_folder, exist_ok=True)
        
        # Thiết lập các tham số cho detection
        self.min_card_area = 5000  # Diện tích tối thiểu của card
        self.max_card_area = 50000  # Diện tích tối đa của card
        self.aspect_ratio_min = 1.4  # Tỷ lệ khung hình tối thiểu (card thường có tỷ lệ ~1.6)
        self.aspect_ratio_max = 2.0  # Tỷ lệ khung hình tối đa
        
    def detect_card_contours(self, frame):
        """
        Detect các contour có thể là business card
        
        Args:
            frame: Frame ảnh từ camera
            
        Returns:
            list: Danh sách các contour được detect
        """
        # Chuyển sang grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Áp dụng Gaussian blur để giảm noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Edge detection
        edges = cv2.Canny(blurred, 50, 150)
        
        # Morphological operations để đóng các khoảng trống
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # Tìm contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        card_contours = []
        
        for contour in contours:
            # Tính diện tích
            area = cv2.contourArea(contour)
            
            # Lọc theo diện tích
            if area < self.min_card_area or area > self.max_card_area:
                continue
            
            # Tính bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # Lọc theo tỷ lệ khung hình
            if aspect_ratio < self.aspect_ratio_min or aspect_ratio > self.aspect_ratio_max:
                continue
            
            # Approximation để kiểm tra xem có phải hình chữ nhật không
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # Card thường có 4 góc
            if len(approx) >= 4:
                card_contours.append({
                    'contour': contour,
                    'area': area,
                    'bbox': (x, y, w, h),
                    'aspect_ratio': aspect_ratio
                })
        
        # Sắp xếp theo diện tích giảm dần
        card_contours.sort(key=lambda x: x['area'], reverse=True)
        
        return card_contours
    
    def save_card_image(self, frame, bbox):
        """
        Lưu ảnh card được detect
        
        Args:
            frame: Frame ảnh gốc
            bbox: Bounding box của card (x, y, w, h)
            
        Returns:
            str: Đường dẫn file ảnh đã lưu
        """
        x, y, w, h = bbox
        
        # Cắt ảnh card từ frame
        card_image = frame[y:y+h, x:x+w]
        
        # Tạo tên file với timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        filename = f"card_{timestamp}.jpg"
        filepath = os.path.join(self.save_folder, filename)
        
        # Lưu ảnh
        cv2.imwrite(filepath, card_image)
        
        return filepath
    
    def draw_detection(self, frame, card_contours):
        """
        Vẽ các detection lên frame
        
        Args:
            frame: Frame ảnh
            card_contours: Danh sách các contour được detect
            
        Returns:
            frame: Frame đã được vẽ detection
        """
        for i, card_info in enumerate(card_contours):
            contour = card_info['contour']
            x, y, w, h = card_info['bbox']
            
            # Vẽ bounding box
            color = (0, 255, 0) if i == 0 else (0, 255, 255)  # Xanh lá cho card tốt nhất, vàng cho các card khác
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # Vẽ contour
            cv2.drawContours(frame, [contour], -1, color, 2)
            
            # Hiển thị thông tin
            info_text = f"Card {i+1}: {card_info['area']:.0f}px²"
            cv2.putText(frame, info_text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        return frame
    
    def run_detection(self, camera_index=1):
        """
        Chạy detection real-time từ camera

        Args:
            camera_index (int): Index của camera (0: webcam tích hợp, 1,2,3...: camera USB)
        """
        # Khởi tạo camera
        print(f"Đang thử kết nối camera index {camera_index}...")
        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            print(f"Lỗi: Không thể mở camera index {camera_index}!")
            print("Thử các camera index khác:")
            for i in range(5):
                test_cap = cv2.VideoCapture(i)
                if test_cap.isOpened():
                    print(f"  - Camera index {i}: Có sẵn")
                    test_cap.release()
                else:
                    print(f"  - Camera index {i}: Không có")
            return
        
        # Thiết lập độ phân giải camera
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

        print(f"✓ Đã kết nối thành công camera index {camera_index}")
        print("Camera đã sẵn sàng! Nhấn 'q' để thoát, 'c' để chụp ảnh thủ công")
        print("Hệ thống sẽ tự động chụp ảnh khi detect được business card")
        
        while True:
            ret, frame = cap.read()
            
            if not ret:
                print("Lỗi: Không thể đọc frame từ camera!")
                break
            
            # Detect business card
            card_contours = self.detect_card_contours(frame)
            
            # Vẽ detection lên frame
            display_frame = self.draw_detection(frame.copy(), card_contours)
            
            # Hiển thị thông tin trên màn hình
            info_text = f"Cards detected: {len(card_contours)}"
            cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # Tự động chụp ảnh nếu detect được card và đã qua thời gian cooldown
            current_time = time.time()
            if (len(card_contours) > 0 and 
                current_time - self.last_capture_time > self.capture_cooldown):
                
                # Chụp ảnh card tốt nhất (có diện tích lớn nhất)
                best_card = card_contours[0]
                filepath = self.save_card_image(frame, best_card['bbox'])
                
                print(f"✓ Đã chụp và lưu card: {filepath}")
                
                # Hiển thị thông báo trên màn hình
                cv2.putText(display_frame, "CAPTURED!", (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                
                self.last_capture_time = current_time
            
            # Hiển thị frame
            cv2.imshow('Business Card Detector', display_frame)
            
            # Xử lý phím nhấn
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('c') and len(card_contours) > 0:
                # Chụp ảnh thủ công
                best_card = card_contours[0]
                filepath = self.save_card_image(frame, best_card['bbox'])
                print(f"✓ Chụp thủ công: {filepath}")
        
        # Giải phóng tài nguyên
        cap.release()
        cv2.destroyAllWindows()
        print("Đã đóng camera và thoát chương trình")

def find_available_cameras():
    """
    Tìm tất cả camera có sẵn trong hệ thống
    """
    print("Đang quét camera có sẵn...")
    available_cameras = []

    for i in range(10):  # Kiểm tra từ index 0 đến 9
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, _ = cap.read()
            if ret:
                available_cameras.append(i)
                print(f"✓ Camera index {i}: Hoạt động tốt")
            else:
                print(f"⚠ Camera index {i}: Có thể kết nối nhưng không đọc được frame")
            cap.release()

    return available_cameras

def main():
    """
    Hàm main để chạy detector
    """
    # Tìm camera có sẵn
    cameras = find_available_cameras()

    if not cameras:
        print("Không tìm thấy camera nào!")
        return

    print(f"\nCác camera có sẵn: {cameras}")

    # Mặc định sử dụng camera USB (thường là index 1)
    # Nếu chỉ có 1 camera thì dùng index 0
    if len(cameras) == 1:
        camera_to_use = cameras[0]
    else:
        # Ưu tiên camera USB (index > 0)
        usb_cameras = [c for c in cameras if c > 0]
        camera_to_use = usb_cameras[0] if usb_cameras else cameras[0]

    print(f"Sử dụng camera index: {camera_to_use}")

    # Khởi tạo detector
    detector = BusinessCardDetector(save_folder="Image")

    # Chạy detection
    detector.run_detection(camera_index=camera_to_use)

if __name__ == "__main__":
    main()
