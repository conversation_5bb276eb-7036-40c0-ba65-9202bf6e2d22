import google.generativeai as genai
import glob
import mimetypes
import json
from pathlib import Path
import pandas as pd

# Cấu hình API key
genai.configure(api_key="AIzaSyDigOhAC3tfNkmDqLxrx7HmdtZAWwOR0QQ")

# Khởi tạo model
model = genai.GenerativeModel("gemini-2.5-flash")

# Tạo folder Data nếu chưa tồn tại
data_folder = Path("Data")
data_folder.mkdir(exist_ok=True)

# Prompt để trích xuất thông tin
prompt = """Hãy trích xuất thông tin từ card visit này và trả về dưới dạng JSON với các trường sau:
- name: Tên người
- title: Chức vụ/Vị trí
- company: Tên công ty
- phone: Số điện thoại
- email: Email

Chỉ trả về JSON hợp lệ. Nếu thông tin nào không có, đặt gi<PERSON> trị null."""

# Danh sách để lưu tất cả kết quả
all_results = []

# Xử lý từng ảnh trong folder Image
for image_path in glob.glob("Image/*.*"):
    # Bỏ qua file app.py
    if image_path.endswith('.py'):
        continue

    print(f"Đang xử lý: {image_path}")

    try:
        # Đọc file ảnh
        with open(image_path, "rb") as f:
            data = f.read()

        # Xác định mime type
        mime = mimetypes.guess_type(image_path)[0] or "image/jpeg"

        # Gửi request đến Gemini
        response = model.generate_content([
            prompt,
            {"mime_type": mime, "data": data}
        ])

        # Parse JSON response
        try:
            # Xử lý response có thể có markdown code block
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                # Loại bỏ markdown code block
                response_text = response_text.replace('```json', '').replace('```', '').strip()

            result = json.loads(response_text)

            # Thêm thông tin file ảnh gốc
            result['image_file'] = Path(image_path).name

            # Thêm vào danh sách kết quả
            all_results.append(result)

            print(f"✓ Đã xử lý: {image_path} - {result.get('name', 'N/A')}")

        except json.JSONDecodeError:
            print(f"✗ Lỗi parse JSON cho {image_path}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"✗ Lỗi xử lý {image_path}: {str(e)}")

# Tạo file Excel từ tất cả kết quả
if all_results:
    df = pd.DataFrame(all_results)

    # Sắp xếp lại thứ tự cột
    column_order = ['name', 'title', 'company', 'phone', 'email']
    df = df.reindex(columns=column_order)

    # Đổi tên cột sang tiếng Việt
    df.columns = ['Tên', 'Chức vụ', 'Công ty', 'Điện thoại', 'Email']

    # Lưu vào file Excel
    excel_file = data_folder / "card_visit_data.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')

    print(f"\n✓ Đã lưu tất cả dữ liệu vào: {excel_file}")
    print(f"✓ Tổng cộng: {len(all_results)} card visit được xử lý")
else:
    print("\n✗ Không có dữ liệu nào để lưu")

print("\nHoàn thành xử lý tất cả ảnh!")